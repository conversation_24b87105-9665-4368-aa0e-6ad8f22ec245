<template>
  <div class="radar-screen">
    <!-- 雷达背景 -->
    <svg class="radar-svg" viewBox="0 0 800 600">
      <!-- 背景渐变 -->
      <defs>
        <radialGradient id="radarGradient" cx="50%" cy="50%" r="50%">
          <stop offset="0%" style="stop-color:#001a2e;stop-opacity:1" />
          <stop offset="70%" style="stop-color:#000a14;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#000000;stop-opacity:1" />
        </radialGradient>
        <filter id="glow">
          <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
          <feMerge> 
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>
      
      <!-- 雷达背景 -->
      <rect width="800" height="600" fill="url(#radarGradient)" />

      <!-- 背景粒子效果 -->
      <g class="background-particles" opacity="0.3">
        <circle v-for="particle in backgroundParticles" :key="particle.id"
          :cx="particle.x" :cy="particle.y" :r="particle.size"
          :fill="particle.color" :opacity="particle.opacity"
          class="animate-pulse">
          <animate attributeName="opacity"
            :values="`${particle.opacity};0;${particle.opacity}`"
            :dur="`${particle.duration}s`"
            repeatCount="indefinite"/>
        </circle>
      </g>
      
      <!-- 雷达网格线 -->
      <g class="radar-grid">
        <!-- 同心圆 -->
        <circle cx="400" cy="300" r="50" fill="none" stroke="#00ff41" stroke-width="0.3" opacity="0.2" />
        <circle cx="400" cy="300" r="100" fill="none" stroke="#00ff41" stroke-width="0.5" opacity="0.4" />
        <circle cx="400" cy="300" r="150" fill="none" stroke="#00ff41" stroke-width="0.5" opacity="0.4" />
        <circle cx="400" cy="300" r="200" fill="none" stroke="#00ff41" stroke-width="0.5" opacity="0.4" />
        <circle cx="400" cy="300" r="250" fill="none" stroke="#00ff41" stroke-width="0.6" opacity="0.5" />

        <!-- 十字线 -->
        <line x1="0" y1="300" x2="800" y2="300" stroke="#00ff41" stroke-width="0.6" opacity="0.4" />
        <line x1="400" y1="0" x2="400" y2="600" stroke="#00ff41" stroke-width="0.6" opacity="0.4" />

        <!-- 对角线 -->
        <line x1="118" y1="88" x2="682" y2="512" stroke="#00ff41" stroke-width="0.4" opacity="0.3" />
        <line x1="682" y1="88" x2="118" y2="512" stroke="#00ff41" stroke-width="0.4" opacity="0.3" />

        <!-- 额外的网格线 -->
        <g opacity="0.2">
          <line x1="200" y1="0" x2="200" y2="600" stroke="#00ff41" stroke-width="0.3" />
          <line x1="600" y1="0" x2="600" y2="600" stroke="#00ff41" stroke-width="0.3" />
          <line x1="0" y1="150" x2="800" y2="150" stroke="#00ff41" stroke-width="0.3" />
          <line x1="0" y1="450" x2="800" y2="450" stroke="#00ff41" stroke-width="0.3" />
        </g>

        <!-- 距离标记 -->
        <g class="distance-markers" opacity="0.6">
          <text x="405" y="200" fill="#00ff41" font-size="8" font-family="monospace">100</text>
          <text x="405" y="150" fill="#00ff41" font-size="8" font-family="monospace">150</text>
          <text x="405" y="100" fill="#00ff41" font-size="8" font-family="monospace">200</text>
          <text x="405" y="50" fill="#00ff41" font-size="8" font-family="monospace">250</text>
        </g>
      </g>
      
      <!-- 雷达扫描线 -->
      <g class="radar-sweep" v-if="showRadarSweep">
        <!-- 主扫描线 -->
        <line
          x1="400" y1="300"
          :x2="400 + 250 * Math.cos(sweepAngle)"
          :y2="300 + 250 * Math.sin(sweepAngle)"
          stroke="#00ff41"
          stroke-width="3"
          opacity="0.9"
          filter="url(#glow)"
        />
        <!-- 扫描扇形区域 -->
        <path
          :d="`M 400 300 L ${400 + 250 * Math.cos(sweepAngle)} ${300 + 250 * Math.sin(sweepAngle)} A 250 250 0 0 1 ${400 + 250 * Math.cos(sweepAngle - 0.4)} ${300 + 250 * Math.sin(sweepAngle - 0.4)} Z`"
          fill="url(#sweepGradient)"
          opacity="0.4"
        />
        <!-- 次级扫描线 -->
        <line
          x1="400" y1="300"
          :x2="400 + 200 * Math.cos(sweepAngle - 0.1)"
          :y2="300 + 200 * Math.sin(sweepAngle - 0.1)"
          stroke="#00ff41"
          stroke-width="1"
          opacity="0.6"
        />
        <defs>
          <linearGradient id="sweepGradient" x1="0%" y1="0%" x2="100%" y2="0%">
            <stop offset="0%" style="stop-color:#00ff41;stop-opacity:0" />
            <stop offset="70%" style="stop-color:#00ff41;stop-opacity:0.3" />
            <stop offset="100%" style="stop-color:#00ff41;stop-opacity:0.6" />
          </linearGradient>
        </defs>
      </g>
      
      <!-- 飞机轨迹 -->
      <g class="aircraft-trails">
        <template v-for="aircraft in aircrafts" :key="`trail-${aircraft.id}`">
          <!-- 主轨迹线 -->
          <polyline
            :points="getTrailPoints(aircraft)"
            fill="none"
            :stroke="getAircraftColor(aircraft)"
            stroke-width="2"
            opacity="0.6"
            stroke-dasharray="3,2"
            filter="url(#glow)"
          />
          <!-- 轨迹点 -->
          <g v-for="(point, index) in aircraft.trail" :key="`point-${index}`">
            <circle
              :cx="point.x"
              :cy="point.y"
              :r="Math.max(0.5, 2 - index * 0.1)"
              :fill="getAircraftColor(aircraft)"
              :opacity="Math.max(0.1, 0.8 - index * 0.04)"
            />
          </g>
        </template>
      </g>
      
      <!-- 冲突警告圆圈 -->
      <g class="conflict-zones">
        <template v-for="conflict in conflicts" :key="`conflict-${conflict.aircraft1.id}-${conflict.aircraft2.id}`">
          <circle 
            :cx="conflict.aircraft1.position.x"
            :cy="conflict.aircraft1.position.y"
            :r="conflict.severity === 'critical' ? 60 : 45"
            fill="none"
            :stroke="conflict.severity === 'critical' ? '#ff4757' : '#ffb000'"
            stroke-width="2"
            opacity="0.6"
            class="animate-ping"
          />
        </template>
      </g>
      
      <!-- 飞机图标 -->
      <g class="aircraft-icons">
        <template v-for="aircraft in aircrafts" :key="aircraft.id">
          <g
            :transform="`translate(${aircraft.position.x}, ${aircraft.position.y}) rotate(${aircraft.heading})`"
            @click="selectAircraft(aircraft)"
            class="aircraft-icon cursor-pointer"
            :class="{ 'selected': selectedAircraft?.id === aircraft.id }"
          >
            <!-- 飞机阴影 -->
            <polygon
              points="1,-7 -5,7 1,5 7,7"
              fill="rgba(0,0,0,0.3)"
              opacity="0.5"
            />
            <!-- 飞机主体 -->
            <polygon
              points="0,-8 -6,6 0,4 6,6"
              :fill="getAircraftColor(aircraft)"
              :stroke="selectedAircraft?.id === aircraft.id ? '#ffffff' : getAircraftColor(aircraft)"
              stroke-width="1.5"
              filter="url(#glow)"
            />
            <!-- 飞机细节 -->
            <line x1="0" y1="-6" x2="0" y2="2" stroke="rgba(255,255,255,0.3)" stroke-width="0.5"/>
            <circle cx="0" cy="-2" r="1" :fill="getAircraftColor(aircraft)" opacity="0.8"/>

            <!-- 速度指示器 -->
            <g v-if="aircraft.speed > 300" opacity="0.6">
              <line x1="-8" y1="0" x2="-12" y2="0" stroke="#ffb000" stroke-width="1"/>
              <line x1="8" y1="0" x2="12" y2="0" stroke="#ffb000" stroke-width="1"/>
            </g>

            <!-- 选中指示器 -->
            <circle
              v-if="selectedAircraft?.id === aircraft.id"
              cx="0" cy="0" r="15"
              fill="none"
              stroke="#ffffff"
              stroke-width="1.5"
              opacity="0.9"
              class="animate-ping-slow"
            />
            <!-- 选中外圈 -->
            <circle
              v-if="selectedAircraft?.id === aircraft.id"
              cx="0" cy="0" r="20"
              fill="none"
              stroke="#00ff41"
              stroke-width="1"
              opacity="0.5"
              class="animate-pulse"
            />
          </g>
        </template>
      </g>
      
      <!-- 飞机标签 -->
      <g class="aircraft-labels">
        <template v-for="aircraft in aircrafts" :key="`label-${aircraft.id}`">
          <g :transform="`translate(${aircraft.position.x + 15}, ${aircraft.position.y - 10})`">
            <!-- 连接线 -->
            <line
              x1="-15" y1="10" x2="0" y2="22"
              stroke="#00ff41"
              stroke-width="0.5"
              opacity="0.6"
            />
            <!-- 标签背景 -->
            <rect
              x="0" y="0"
              :width="getLabelWidth(aircraft)"
              height="50"
              fill="rgba(0, 0, 0, 0.85)"
              stroke="#00ff41"
              stroke-width="0.8"
              rx="3"
              filter="url(#glow)"
            />
            <!-- 标签边框装饰 -->
            <rect
              x="1" y="1"
              :width="getLabelWidth(aircraft) - 2"
              height="48"
              fill="none"
              stroke="rgba(0, 255, 65, 0.3)"
              stroke-width="0.5"
              rx="2"
            />
            <!-- 标签文本 -->
            <text x="5" y="13" fill="#00ff41" font-size="11" font-family="monospace" font-weight="bold">
              {{ aircraft.callsign }}
            </text>
            <text x="5" y="26" fill="#ffb000" font-size="9" font-family="monospace">
              FL{{ Math.round(aircraft.altitude/100) }}
            </text>
            <text x="5" y="38" fill="#3742fa" font-size="9" font-family="monospace">
              {{ aircraft.speed }}kt
            </text>
            <!-- 航向指示 -->
            <text x="5" y="48" fill="#ff6b6b" font-size="8" font-family="monospace">
              {{ Math.round(aircraft.heading) }}°
            </text>
            <!-- 状态指示器 -->
            <circle
              :cx="getLabelWidth(aircraft) - 8"
              cy="8"
              r="2"
              :fill="getAircraftColor(aircraft)"
              class="animate-pulse"
            />
          </g>
        </template>
      </g>
      
      <!-- 罗盘 -->
      <g class="compass" transform="translate(50, 50)">
        <circle cx="0" cy="0" r="30" fill="rgba(0, 0, 0, 0.5)" stroke="#00ff41" stroke-width="1" />
        <text x="-3" y="-20" fill="#00ff41" font-size="12" font-weight="bold">N</text>
        <text x="22" y="5" fill="#00ff41" font-size="12" font-weight="bold">E</text>
        <text x="-3" y="25" fill="#00ff41" font-size="12" font-weight="bold">S</text>
        <text x="-25" y="5" fill="#00ff41" font-size="12" font-weight="bold">W</text>
        <line x1="0" y1="-25" x2="0" y2="-15" stroke="#00ff41" stroke-width="2" />
        <line x1="20" y1="0" x2="15" y2="0" stroke="#00ff41" stroke-width="1" />
        <line x1="0" y1="20" x2="0" y2="15" stroke="#00ff41" stroke-width="1" />
        <line x1="-20" y1="0" x2="-15" y2="0" stroke="#00ff41" stroke-width="1" />
      </g>
      
      <!-- 距离标尺 -->
      <g class="range-scale" transform="translate(750, 550)">
        <text x="0" y="0" fill="#00ff41" font-size="10" text-anchor="end">50nm</text>
        <line x1="-40" y1="5" x2="0" y2="5" stroke="#00ff41" stroke-width="1" />
        <line x1="-40" y1="3" x2="-40" y2="7" stroke="#00ff41" stroke-width="1" />
        <line x1="0" y1="3" x2="0" y2="7" stroke="#00ff41" stroke-width="1" />
      </g>
    </svg>
    
    <!-- 雷达信息面板 -->
    <div class="radar-info">
      <div class="info-item">
        <span class="label">扫描频率:</span>
        <span class="value">{{ scanRate }} rpm</span>
      </div>
      <div class="info-item">
        <span class="label">显示范围:</span>
        <span class="value">50 nm</span>
      </div>
      <div class="info-item">
        <span class="label">更新间隔:</span>
        <span class="value">100 ms</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  aircrafts: Array,
  selectedAircraft: Object,
  conflicts: Array
})

const emit = defineEmits(['select-aircraft'])

const showRadarSweep = ref(true)
const sweepAngle = ref(0)
const scanRate = ref(15)
const backgroundParticles = ref([])

// 生成背景粒子
const generateBackgroundParticles = () => {
  const particles = []
  for (let i = 0; i < 30; i++) {
    particles.push({
      id: i,
      x: Math.random() * 800,
      y: Math.random() * 600,
      size: Math.random() * 1.5 + 0.5,
      color: ['#00ff41', '#ffb000', '#3742fa'][Math.floor(Math.random() * 3)],
      opacity: Math.random() * 0.5 + 0.1,
      duration: Math.random() * 3 + 2
    })
  }
  backgroundParticles.value = particles
}

// 雷达扫描动画
let sweepInterval = null
const startRadarSweep = () => {
  sweepInterval = setInterval(() => {
    sweepAngle.value += 0.1
    if (sweepAngle.value >= Math.PI * 2) {
      sweepAngle.value = 0
    }
  }, 50)
}

const stopRadarSweep = () => {
  if (sweepInterval) {
    clearInterval(sweepInterval)
    sweepInterval = null
  }
}

// 获取飞机颜色
const getAircraftColor = (aircraft) => {
  if (aircraft.status === 'emergency') return '#ff4757'
  if (aircraft.altitude < 5000) return '#ffb000'
  if (aircraft.speed > 400) return '#3742fa'
  return '#00ff41'
}

// 获取轨迹点
const getTrailPoints = (aircraft) => {
  return aircraft.trail.map(point => `${point.x},${point.y}`).join(' ')
}

// 获取标签宽度
const getLabelWidth = (aircraft) => {
  const callsignLength = aircraft.callsign.length
  return Math.max(callsignLength * 6 + 8, 60)
}

// 选择飞机
const selectAircraft = (aircraft) => {
  emit('select-aircraft', aircraft)
}

onMounted(() => {
  startRadarSweep()
  generateBackgroundParticles()
})

onUnmounted(() => {
  stopRadarSweep()
})
</script>

<style scoped>
.radar-screen {
  @apply relative w-full h-full bg-black overflow-hidden;
}

.radar-svg {
  @apply w-full h-full;
}

.aircraft-icon:hover {
  @apply drop-shadow-lg;
}

.aircraft-icon.selected polygon {
  @apply drop-shadow-lg;
}

.radar-info {
  @apply absolute top-4 right-4 bg-black bg-opacity-50 border border-radar-green rounded p-3 text-xs font-mono;
}

.info-item {
  @apply flex justify-between mb-1;
}

.label {
  @apply text-slate-400 mr-2;
}

.value {
  @apply text-radar-green font-semibold;
}

.radar-sweep {
  animation: radar-sweep 4s linear infinite;
}

@keyframes radar-sweep {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style> 