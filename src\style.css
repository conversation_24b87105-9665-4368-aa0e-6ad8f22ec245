@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  overflow: hidden;
}

#app {
  width: 100vw;
  height: 100vh;
  text-align: left;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.3);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 65, 0.5);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 65, 0.8);
}

/* 华丽的动画效果 */
@keyframes matrix-rain {
  0% { transform: translateY(-100vh); opacity: 1; }
  100% { transform: translateY(100vh); opacity: 0; }
}

@keyframes glow-pulse {
  0%, 100% {
    box-shadow: 0 0 5px rgba(0, 255, 65, 0.5),
                0 0 10px rgba(0, 255, 65, 0.3),
                0 0 15px rgba(0, 255, 65, 0.1);
  }
  50% {
    box-shadow: 0 0 10px rgba(0, 255, 65, 0.8),
                0 0 20px rgba(0, 255, 65, 0.6),
                0 0 30px rgba(0, 255, 65, 0.4);
  }
}

@keyframes data-flow {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.glow-effect {
  animation: glow-pulse 2s ease-in-out infinite;
}

.matrix-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(0, 255, 65, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 255, 65, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(0, 255, 65, 0.05) 0%, transparent 50%);
  pointer-events: none;
  z-index: 1;
}