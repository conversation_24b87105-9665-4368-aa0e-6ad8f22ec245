{"name": "lucide-vue-next", "version": "0.294.0", "author": "<PERSON>", "description": "A Lucide icon library package for Vue 3 applications", "license": "ISC", "homepage": "https://lucide.dev", "bugs": "https://github.com/lucide-icons/lucide/issues", "repository": {"type": "git", "url": "https://github.com/lucide-icons/lucide.git", "directory": "packages/lucide-vue-next"}, "keywords": ["Lucide", "Angular", "<PERSON><PERSON>", "Icons", "Icon", "SVG", "Feather Icons", "Fontawesome", "Font Awesome"], "amdName": "lucide-vue-next", "source": "build/lucide-vue-next.js", "main": "dist/cjs/lucide-vue-next.js", "main:umd": "dist/umd/lucide-vue-next.js", "module": "dist/esm/lucide-vue-next.js", "unpkg": "dist/umd/lucide-vue-next.min.js", "typings": "dist/lucide-vue-next.d.ts", "sideEffects": false, "files": ["dist", "nuxt.js"], "devDependencies": {"@testing-library/jest-dom": "^6.1.4", "@testing-library/vue": "^6.6.1", "@vue/compiler-sfc": "3.2.45", "@vue/test-utils": "2.2.4", "@vitejs/plugin-vue": "^3.2.0", "rollup": "^3.5.1", "vite": "^4.3.9", "vitest": "^0.32.2", "vue": "3.2.45", "jsdom": "^20.0.3", "@lucide/rollup-plugins": "1.0.0", "@lucide/build-icons": "1.0.0"}, "peerDependencies": {"vue": ">=3.0.1"}, "scripts": {"build": "pnpm clean && pnpm copy:license && pnpm build:icons && pnpm build:bundles && pnpm build:types", "copy:license": "cp ../../LICENSE ./LICENSE", "clean": "rm -rf dist && rm -rf ./src/icons/*.ts", "build:icons": "build-icons --output=./src --templateSrc=./scripts/exportTemplate.mjs --renderUniqueKey --withAliases --aliasesFileExtension=.ts --iconFileExtension=.ts --exportFileName=index.ts", "build:types": "node ./scripts/buildTypes.mjs", "build:bundles": "rollup -c ./rollup.config.mjs", "test": "vitest run", "version": "pnpm version --git-tag-version=false"}}