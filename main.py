import pandas as pd
import numpy as np
import lightgbm as lgb
from sklearn.preprocessing import LabelEncoder
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_log_error

# 1. 读取数据
train = pd.read_csv('data/train.csv', parse_dates=['date'])
test = pd.read_csv('data/test.csv', parse_dates=['date'])
stores = pd.read_csv('data/stores.csv')
oil = pd.read_csv('data/oil.csv', parse_dates=['date'])
transactions = pd.read_csv('data/transactions.csv', parse_dates=['date'])
holidays = pd.read_csv('data/holidays_events.csv', parse_dates=['date'])
sample_submission = pd.read_csv('data/sample_submission.csv')

# 2. 简单缺失值处理
oil['dcoilwtico'].fillna(method='ffill', inplace=True)

# 3. 合并元数据
train = train.merge(stores, on='store_nbr', how='left')
train = train.merge(transactions, on=['date', 'store_nbr'], how='left')
train = train.merge(oil, on='date', how='left')

test = test.merge(stores, on='store_nbr', how='left')
test = test.merge(transactions, on=['date', 'store_nbr'], how='left')
test = test.merge(oil, on='date', how='left')

# 4. 时间特征
def create_date_features(df):
    df['day'] = df['date'].dt.day
    df['weekday'] = df['date'].dt.weekday
    df['month'] = df['date'].dt.month
    df['year'] = df['date'].dt.year
    df['is_weekend'] = df['weekday'].isin([5, 6])
    df['is_month_start'] = df['date'].dt.is_month_start.astype('int')
    df['is_month_end'] = df['date'].dt.is_month_end.astype('int')
    return df

train = create_date_features(train)
test = create_date_features(test)

# 5. 节假日是否休假（可选增强）
holidays = holidays[holidays['transferred'] == False]
holidays['is_holiday'] = 1
train = train.merge(holidays[['date', 'is_holiday']], on='date', how='left')
test = test.merge(holidays[['date', 'is_holiday']], on='date', how='left')
train['is_holiday'] = train['is_holiday'].fillna(0)
test['is_holiday'] = test['is_holiday'].fillna(0)

# 6. 编码分类变量
for col in ['family', 'city', 'state', 'type']:
    le = LabelEncoder()
    train[col] = le.fit_transform(train[col])
    test[col] = le.transform(test[col])

# 7. 填补缺失值
train['transactions'] = train['transactions'].fillna(0)
test['transactions'] = test['transactions'].fillna(0)

# 8. 准备训练数据
features = [
    'store_nbr', 'onpromotion', 'transactions', 'dcoilwtico',
    'family', 'city', 'state', 'type', 'cluster',
    'day', 'weekday', 'month', 'year', 'is_weekend',
    'is_month_start', 'is_month_end', 'is_holiday'
]

target = 'sales'

train[target] = train[target].clip(lower=0)
train[target] = np.log1p(train[target])


X = train[features]
y = train[target]

# 9. 拆分训练集和验证集
X_train, X_val, y_train, y_val = train_test_split(X, y, test_size=0.2, shuffle=False)

# 10. LightGBM 模型训练
model = lgb.LGBMRegressor(n_estimators=1000, learning_rate=0.05, random_state=42)
model.fit(X_train, y_train,
          eval_set=[(X_val, y_val)])

# 11. 在测试集上预测
test_preds = model.predict(test[features])
test_preds = np.expm1(test_preds)  # 反变换
test_preds = np.clip(test_preds, 0, None)  # 销售不能为负

# 12. 生成提交文件
submission = sample_submission.copy()
submission['sales'] = test_preds
submission.to_csv('submission.csv', index=False)
